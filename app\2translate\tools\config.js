/**
 * Configuration module for Claude 4 verification system
 */

export const CLAUDE4_CONFIG = {
  // Claude 4 API configuration
  MODEL: "claude-3-5-sonnet-20241022",
  MAX_TOKENS: 8192,
  TEMPERATURE: 0.3, // Lower temperature for more consistent verification
  
  // Verification settings
  VERIFICATION_ENABLED: process.env.CLAUDE4_VERIFICATION_ENABLED === 'true',
  VERIFICATION_MODE: process.env.CLAUDE4_VERIFICATION_MODE || 'full', // 'full', 'sample', 'disabled'
  SAMPLE_RATE: parseFloat(process.env.CLAUDE4_SAMPLE_RATE) || 0.2, // 20% of chunks when in sample mode
  
  // Quality thresholds
  MIN_ACCURACY_SCORE: parseFloat(process.env.MIN_ACCURACY_SCORE) || 0.8,
  MIN_FLUENCY_SCORE: parseFloat(process.env.MIN_FLUENCY_SCORE) || 0.8,
  MIN_CULTURAL_SCORE: parseFloat(process.env.MIN_CULTURAL_SCORE) || 0.7,
  
  // Retry settings
  MAX_VERIFICATION_RETRIES: parseInt(process.env.MAX_VERIFICATION_RETRIES) || 3,
  VERIFICATION_RETRY_DELAY: parseInt(process.env.VERIFICATION_RETRY_DELAY) || 5000,
  
  // Logging
  LOG_VERIFICATION_RESULTS: process.env.LOG_VERIFICATION_RESULTS !== 'false',
  DETAILED_LOGGING: process.env.DETAILED_VERIFICATION_LOGGING === 'true',
  
  // Improvement tracking
  TRACK_IMPROVEMENTS: process.env.TRACK_IMPROVEMENTS !== 'false',
  IMPROVEMENT_LOG_PATH: process.env.IMPROVEMENT_LOG_PATH || 'app/logs/improvements.log',
  
  // Discord notifications for verification issues
  NOTIFY_VERIFICATION_ISSUES: process.env.NOTIFY_VERIFICATION_ISSUES === 'true',
  VERIFICATION_WEBHOOK: process.env.VERIFICATION_DISCORD_WEBHOOK || process.env.DEV_DISCORD_WEBHOOK,
};

export const VERIFICATION_TOOLS = [
  {
    name: "translation_accuracy_checker",
    description: "Analyze translation accuracy by comparing source and target meaning",
    input_schema: {
      type: "object",
      properties: {
        source_text: { type: "string", description: "Original English text" },
        translated_text: { type: "string", description: "Polish translation to verify" },
        context: { type: "string", description: "Additional context (character details, genre, etc.)" }
      },
      required: ["source_text", "translated_text"]
    }
  },
  {
    name: "cultural_context_validator",
    description: "Validate cultural appropriateness and context preservation in translation",
    input_schema: {
      type: "object",
      properties: {
        source_text: { type: "string", description: "Original English text" },
        translated_text: { type: "string", description: "Polish translation to verify" },
        anime_genre: { type: "string", description: "Anime genre for context" },
        character_details: { type: "string", description: "Character information" }
      },
      required: ["source_text", "translated_text"]
    }
  },
  {
    name: "polish_grammar_analyzer",
    description: "Analyze Polish grammar, style, and linguistic correctness",
    input_schema: {
      type: "object",
      properties: {
        translated_text: { type: "string", description: "Polish translation to analyze" },
        context: { type: "string", description: "Context for style appropriateness" }
      },
      required: ["translated_text"]
    }
  },
  {
    name: "consistency_checker",
    description: "Check consistency of terminology, character names, and style across chunks",
    input_schema: {
      type: "object",
      properties: {
        current_chunk: { type: "string", description: "Current translation chunk" },
        previous_chunks: { type: "array", items: { type: "string" }, description: "Previous translation chunks for consistency" },
        terminology_glossary: { type: "string", description: "Established terminology and character names" }
      },
      required: ["current_chunk"]
    }
  },
  {
    name: "improvement_suggester",
    description: "Generate specific improvement suggestions for Claude 3.5 translations",
    input_schema: {
      type: "object",
      properties: {
        source_text: { type: "string", description: "Original English text" },
        translated_text: { type: "string", description: "Polish translation to improve" },
        identified_issues: { type: "array", items: { type: "string" }, description: "List of identified issues" },
        context: { type: "string", description: "Additional context" }
      },
      required: ["source_text", "translated_text", "identified_issues"]
    }
  }
];

export default { CLAUDE4_CONFIG, VERIFICATION_TOOLS };
